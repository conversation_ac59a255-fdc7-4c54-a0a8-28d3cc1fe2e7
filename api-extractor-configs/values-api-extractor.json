{
  "extends": "./api-extractor-base.json",
  "mainEntryPointFilePath": "<projectFolder>/dist/types/values/index.d.ts",
  "dtsRollup": {
    "untrimmedFilePath": "<projectFolder>/dist/types/values/values-internal.d.ts",
    "publicTrimmedFilePath": "<projectFolder>/dist/types/values/values.d.ts"
  },
  /**
   * Enable the apiReport but use the same reportFolder and tempFolder to make it a no-op.
   * This way forgotten exports are a warning instead of an error.
   */
  "apiReport": {
    "enabled": true,
    "reportFileName": "values-tmp.api.md",
    "reportFolder": "temp",
    "reportTempFolder": "temp"
  }
}
