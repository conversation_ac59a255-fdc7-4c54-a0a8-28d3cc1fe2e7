{
  "extends": "./api-extractor-base.json",
  "mainEntryPointFilePath": "<projectFolder>/dist/types/react-auth0/index.d.ts",
  "dtsRollup": {
    "untrimmedFilePath": "<projectFolder>/dist/types/react-auth0/react-auth0-internal.d.ts",
    "publicTrimmedFilePath": "<projectFolder>/dist/types/react-auth0/react-auth0.d.ts"
  },
  /**
   * Enable the apiReport but use the same reportFolder and tempFolder to make it a no-op.
   * This way forgotten exports are a warning instead of an error.
   */
  "apiReport": {
    "enabled": true,
    "reportFileName": "react-auth0-tmp.api.md",
    "reportFolder": "temp",
    "reportTempFolder": "temp"
  }
}
