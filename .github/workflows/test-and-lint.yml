name: Test and lint
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

on:
  push:
    branches: [main]
  pull_request:
    branches: ["**"]

jobs:
  check:
    name: Test and lint
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - uses: actions/checkout@v4

      - name: Node setup
        uses: actions/setup-node@v4
        with:
          node-version: "18.20.8"

      - name: NPM v8
        run: npm install -g npm@8 --registry=https://registry.npmjs.org

      - run: npm i

      - run: npm run test

      - run: npm run test-esm

      - run: npm run format-check
