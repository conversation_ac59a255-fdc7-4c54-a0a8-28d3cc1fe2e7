{"$schema": "http://json-schema.org/draft-07/schema#", "description": "Configuration schema for Convex project settings.\n\nDocumentation: https://docs.convex.dev/production/project-configuration#convexjson", "type": "object", "properties": {"$schema": {"type": "string"}, "node": {"type": "object", "description": "Node.js specific configuration for server-side packages and dependencies.", "properties": {"externalPackages": {"type": ["array"], "description": "List of packages that should be installed on the server instead of being bundled. Use this for packages that can't be bundled or need to be installed directly on the server.\n\nIf you want to mark all dependencies as external, you can use the string '*' instead of an array.\n\nDocumentation: https://docs.convex.dev/functions/bundling#external-packages", "items": {"type": "string"}, "default": ["*"], "oneOf": [{"contains": {"const": "*"}, "maxItems": 1}, {"not": {"contains": {"const": "*"}}}]}}}, "generateCommonJSApi": {"type": "boolean", "description": "When true, generates CommonJS-compatible API files for projects not using ES modules. Enable this if your project uses require() syntax instead of ES modules.\n\nDocumentation: https://docs.convex.dev/client/javascript/node#javascript-with-commonjs-require-syntax", "default": false}, "functions": {"type": "string", "description": "Path to the directory containing Convex functions. You can customize this to use a different location than the default 'convex/' directory.\n\nDocumentation: https://docs.convex.dev/production/project-configuration#changing-the-convex-folder-name-or-location", "default": "convex/"}}, "additionalProperties": false}