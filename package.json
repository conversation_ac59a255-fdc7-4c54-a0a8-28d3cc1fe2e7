{"name": "convex", "description": "Client for the Convex Cloud", "version": "1.24.8", "author": "Convex, Inc. <<EMAIL>>", "homepage": "https://convex.dev", "repository": {"type": "git", "url": "git+https://github.com/get-convex/convex-js.git"}, "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/cjs-types/index.d.ts", "type": "module", "sideEffects": false, "exports": {".": {"require": {"convex-internal-types": {"types": "./dist/internal-cjs-types/index.d.ts"}, "types": "./dist/cjs-types/index.d.ts", "require": "./dist/cjs/index.js"}, "import": {"convex-internal-types": {"types": "./dist/internal-esm-types/index.d.ts"}, "types": "./dist/esm-types/index.d.ts", "import": "./dist/esm/index.js"}}, "./server": {"require": {"convex-internal-types": {"types": "./dist/internal-cjs-types/server/index.d.ts"}, "types": "./dist/cjs-types/server/index.d.ts", "require": "./dist/cjs/server/index.js"}, "import": {"convex-internal-types": {"types": "./dist/internal-esm-types/server/index.d.ts"}, "types": "./dist/esm-types/server/index.d.ts", "import": "./dist/esm/server/index.js"}}, "./react": {"require": {"convex-internal-types": {"types": "./dist/internal-cjs-types/react/index.d.ts"}, "types": "./dist/cjs-types/react/index.d.ts", "require": "./dist/cjs/react/index.js"}, "import": {"convex-internal-types": {"types": "./dist/internal-esm-types/react/index.d.ts"}, "types": "./dist/esm-types/react/index.d.ts", "import": "./dist/esm/react/index.js"}}, "./react-auth0": {"require": {"convex-internal-types": {"types": "./dist/internal-cjs-types/react-auth0/index.d.ts"}, "types": "./dist/cjs-types/react-auth0/index.d.ts", "require": "./dist/cjs/react-auth0/index.js"}, "import": {"convex-internal-types": {"types": "./dist/internal-esm-types/react-auth0/index.d.ts"}, "types": "./dist/esm-types/react-auth0/index.d.ts", "import": "./dist/esm/react-auth0/index.js"}}, "./react-clerk": {"require": {"convex-internal-types": {"types": "./dist/internal-cjs-types/react-clerk/index.d.ts"}, "types": "./dist/cjs-types/react-clerk/index.d.ts", "require": "./dist/cjs/react-clerk/index.js"}, "import": {"convex-internal-types": {"types": "./dist/internal-esm-types/react-clerk/index.d.ts"}, "types": "./dist/esm-types/react-clerk/index.d.ts", "import": "./dist/esm/react-clerk/index.js"}}, "./nextjs": {"require": {"convex-internal-types": {"types": "./dist/internal-cjs-types/nextjs/index.d.ts"}, "types": "./dist/cjs-types/nextjs/index.d.ts", "require": "./dist/cjs/nextjs/index.js"}, "import": {"convex-internal-types": {"types": "./dist/internal-esm-types/nextjs/index.d.ts"}, "types": "./dist/esm-types/nextjs/index.d.ts", "import": "./dist/esm/nextjs/index.js"}}, "./browser": {"require": {"convex-internal-types": {"types": "./dist/internal-cjs-types/browser/index.d.ts"}, "types": "./dist/cjs-types/browser/index.d.ts", "node": "./dist/cjs/browser/index-node.js", "require": "./dist/cjs/browser/index.js"}, "import": {"convex-internal-types": {"types": "./dist/internal-esm-types/browser/index.d.ts"}, "types": "./dist/esm-types/browser/index.d.ts", "node": "./dist/esm/browser/index-node.js", "import": "./dist/esm/browser/index.js"}}, "./values": {"require": {"convex-internal-types": {"types": "./dist/internal-cjs-types/values/index.d.ts"}, "types": "./dist/cjs-types/values/index.d.ts", "require": "./dist/cjs/values/index.js"}, "import": {"convex-internal-types": {"types": "./dist/internal-esm-types/values/index.d.ts"}, "types": "./dist/esm-types/values/index.d.ts", "import": "./dist/esm/values/index.js"}}, "./package.json": "./package.json"}, "@comment typesVersions": ["Thi types field in exports is only supported with moduleResolution", "set to node16 or nodenext so typesVersions is still useful."], "typesVersions": {"*": {"*": ["./dist/internal-cjs-types/index.d.ts"], "server": ["./dist/internal-cjs-types/server/index.d.ts"], "react": ["./dist/internal-cjs-types/react/index.d.ts"], "react-auth0": ["./dist/internal-cjs-types/react-auth0/index.d.ts"], "react-clerk": ["./dist/internal-cjs-types/react-clerk/index.d.ts"], "nextjs": ["./dist/internal-cjs-types/nextjs/index.d.ts"], "browser": ["./dist/internal-cjs-types/browser/index.d.ts"], "values": ["./dist/internal-cjs-types/values/index.d.ts"]}}, "bin": {"convex": "bin/main-dev", "convex-bundled": "bin/main.js"}, "scripts": {"build": "python3 scripts/build.py 2>&1", "bundle-server": "node scripts/bundle-server.mjs", "clean": "shx rm -rf dist tmpDist*", "lint": "eslint .", "format": "prettier -w . && eslint --fix .", "format-check": "prettier -c . && eslint .", "prepare": "npm run build", "prepack": "node scripts/prepack.mjs", "postpack": "node scripts/postpack.mjs", "test": "vitest --silent", "test-not-silent": "vitest", "new-test": "vitest", "test-esm": "node ./scripts/test-esm.mjs && ./scripts/checkdeps.mjs && ./scripts/checkimports.mjs"}, "files": ["bin", "browser", "config", "dist", "nextjs", "react", "react-auth0", "react-clerk", "server", "src", "values", "CHANGELOG.md", "LICENSE", "README.md", "browser-bundle.js", "package.json", "tsconfig.json", "!*.test.*", "!test", "!test_fixtures", "!client_node_test_helpers.*", "!src/bundler/.eslintrc.cjs"], "keywords": ["convex", "database", "react", "state", "serverless"], "license": "Apache-2.0", "@comment dependencies": ["CLI tool dependencies are listed in devDependencies instead."], "dependencies": {"esbuild": "0.25.4", "jwt-decode": "^4.0.0", "prettier": "3.5.3"}, "peerDependencies": {"@auth0/auth0-react": "^2.0.1", "@clerk/clerk-react": "^4.12.8 || ^5.0.0", "react": "^18.0.0 || ^19.0.0-0 || ^19.0.0"}, "peerDependenciesMeta": {"react": {"optional": true}, "@auth0/auth0-react": {"optional": true}, "@clerk/clerk-react": {"optional": true}}, "@comment devDependencies": ["The dependencies of the CLI are also in devDependencies, built into", "a bundle."], "devDependencies": {"@auth0/auth0-react": "2.3.0", "@babel/parser": "^7.27.1", "@babel/types": "^7.27.1", "@clerk/clerk-react": "^5.20.0", "@commander-js/extra-typings": "^11.1.0", "@eslint/compat": "~1.2.4", "@eslint/eslintrc": "~3.3.0", "@eslint/js": "~9.26.0", "@modelcontextprotocol/sdk": "^1.2.0", "@octokit/openapi-types": "~25.0.0", "@sentry/node": "^7.23.0", "@sentry/tracing": "^7.23.0", "@swc/core": "1.10.4", "@testing-library/react": "~16.3.0", "@types/adm-zip": "^0.5.7", "@types/deep-equal": "1.0.4", "@types/detect-port": "~1.3.5", "@types/inquirer": "^9.0.0", "@types/jwt-encode": "~1.0.0", "@types/node": "^18.17.0", "@types/progress": "~2.0.7", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/semver": "^7.3.13", "@types/serve-handler": "~6.1.4", "@types/ws": "^8.5.13", "@vitest/eslint-plugin": "~1.1.15", "adm-zip": "^0.5.10", "bufferutil": "^4.0.7", "chalk": "5", "chokidar": "3.6.0", "commander": "^11.1.0", "deep-equal": "2.2.3", "depcheck": "~1.4.7", "detect-port": "^2.1.0", "dotenv": "^16.4.7", "encoding": "^0.1.13", "esbuild": "0.25.4", "esbuild-plugin-external-global": "~1.0.1", "eslint": "9.26.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0-beta-26f2496093-20240514", "eslint-plugin-require-extensions": "~0.1.3", "fetch-retry": "~6.0.0", "find-up": "^7.0.0", "globals": "~15.15.0", "happy-dom": "~17.4.0", "inquirer": "^9.1.4", "inquirer-search-list": "~1.2.6", "jsdom": "~26.1.0", "json5": "~2.2.3", "jwt-encode": "~1.0.1", "knip": "~5.53.0", "napi-wasm": "1.1.3", "open": "^8.3.0", "openid-client": "^5.3.1", "ora": "^8.1.1", "progress": "~2.0.3", "react": "^18.0.0", "react-dom": "^18.0.0", "semver": "^7.6.0", "serve-handler": "~6.1.6", "shx": "~0.3.4", "skott": "~0.35.4", "strip-ansi": "^7.0.1", "tar": "^7.4.3", "tsx": "^4.19.4", "typedoc": "^0.24.6", "typescript": "~5.0.3", "typescript-eslint": "~8.17.0", "utf-8-validate": "^5.0.10", "vitest": "^3.1.4", "wait-for-expect": "~3.0.2", "ws": "8.18.0", "zod": "^3.24.0", "zod-to-json-schema": "^3.24.0"}, "engines": {"npm": ">=7.0.0", "node": ">=18.0.0"}}